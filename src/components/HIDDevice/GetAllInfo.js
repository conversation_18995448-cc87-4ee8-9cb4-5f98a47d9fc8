import GetKeymap from "../Keymap/GetKeymap";
import GetLight from "../Light/GetLight";
import GetPerformance from "../Performance/GetPerformance";
import GetAdvancedKey from "../AdvancedKey/GetAdvancedKey";
import GetVersion from "../Settings/GetVersion";
import GetProfile from "../Profile/GetProfile";
import GetStableMode from "../StableMode/GetStableMode";
import GetSn from "../Settings/GetSn";
import GetKeyVoltage from "../KeyTest/GetKeyVoltage";
import GetTopBottomVoltage from "../KeyTest/GetTopBottomVoltage";
import GetFullKeyNoClick from "../Settings/GetFullKeyNoClick/GetFullKeyNoClick";
import GetMacro from "../Macro/GetMacro";

const GetAllInfo = (dataQueue) => {
  GetVersion(dataQueue);
  GetSn(dataQueue);
  GetProfile(dataQueue);
  GetStableMode(dataQueue);
  GetKeymap(dataQueue);
  GetLight(dataQueue);
  GetPerformance(dataQueue);
  GetAdvancedKey(dataQueue);
  GetKeyVoltage(dataQueue);
  GetTopBottomVoltage(dataQueue);
  GetFullKeyNoClick(dataQueue);
  GetMacro(dataQueue);
};

export default GetAllInfo;