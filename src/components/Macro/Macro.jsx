import React, { useState, useEffect, useRef } from 'react';
import { Button, Card, Modal, message, Typography, Space, Tag, Select, Divider, Table, Input, InputNumber, Radio, Popconfirm, theme } from 'antd';
import { StopOutlined, DeleteOutlined, EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { setMacroConfig, encodeMacroData, parseMacroBuffer } from './SetMacro';
import { getHIDKeyCode, getKeyName } from './MacroKeyMap';
import './Macro.css';
import { findNameByCode } from '../../utils/hidUtils';
import { ConfigProvider } from 'antd';
import pressIcon from '../../assets/macro/press.svg';
import releaseIcon from '../../assets/macro/release.svg';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;

const Macro = () => {
  const { t } = useTranslation();
  const { addToQueue, dataQueue, macro, setMacro } = useHandleDevice();
  const [isRecording, setIsRecording] = useState(false);
  const [currentMacro, setCurrentMacro] = useState(0); // 默认选中M0
  const [recordedActions, setRecordedActions] = useState([]);
  const [macroList, setMacroList] = useState([]);
  const recordStartTime = useRef(null);
  const lastActionTime = useRef(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [editingKey, setEditingKey] = useState(null); // 正在编辑的行的key
  const [editingDuration, setEditingDuration] = useState(0);

  // 新增：间隔时间设置
  // delayMode: 'recorded' | 'fixed'
  const [delayMode, setDelayMode] = useState('recorded');
  const [fixedDelay, setFixedDelay] = useState(50);

  // 初始化M0-M15宏键列表
  useEffect(() => {
    // 解析设备返回的宏数据
    const parsedActions = parseMacroBuffer(macro.buffer);
    // parsedActions 应为长度为16的数组，每个元素为 { actions: [...] }
    // 需要和M0-M15一一对应
    if (Array.isArray(parsedActions) && parsedActions.length === 16) {
      const macros = parsedActions.map((item, idx) => ({
        id: `M${idx}`,
        name: `M${idx}`,
        actions: Array.isArray(item.actions) ? item.actions : [],
        description: `${t('macro.macro_key')} ${idx}`
      }));
      setMacroList(macros);
    } else {
      // fallback: 保证有16个宏键
      const macros = [];
      for (let i = 0; i < 16; i++) {
        macros.push({
          id: `M${i}`,
          name: `M${i}`,
          actions: [],
          description: `${t('macro.macro_key')} ${i}`
        });
      }
      setMacroList(macros);
    }
  }, [macro]);

  // 监听键盘事件进行录制
  useEffect(() => {
    if (!isRecording) return;

    const handleKeyDown = (event) => {
      if (!isRecording) return;

      const now = Date.now();
      if (!recordStartTime.current) {
        recordStartTime.current = now;
        lastActionTime.current = now;
      }

      // 计算与上一个动作的时间间隔
      const timeSinceLastAction = now - lastActionTime.current;

      // 如果间隔大于10ms，添加延时动作
      if (timeSinceLastAction > 10 && recordedActions.length > 0) {
        setRecordedActions(prev => [...prev, {
          type: 'delay',
          duration: delayMode === 'fixed' ? fixedDelay : timeSinceLastAction,
          timestamp: now - recordStartTime.current
        }]);
      }

      // 添加按键按下动作
      const hidKeyCode = getHIDKeyCode(event.keyCode);
      if (hidKeyCode !== 0x00) {
        setRecordedActions(prev => [...prev, {
          type: 'keydown',
          keyCode: hidKeyCode,
          jsKeyCode: event.keyCode,
          key: getKeyName(event.keyCode),
          timestamp: now - recordStartTime.current
        }]);
      }

      lastActionTime.current = now;
      event.preventDefault();
    };

    const handleKeyUp = (event) => {
      if (!isRecording) return;

      const now = Date.now();
      const timeSinceLastAction = now - lastActionTime.current;

      // 如果间隔大于10ms，添加延时动作
      if (timeSinceLastAction > 10) {
        setRecordedActions(prev => [...prev, {
          type: 'delay',
          duration: delayMode === 'fixed' ? fixedDelay : timeSinceLastAction,
          timestamp: now - recordStartTime.current
        }]);
      }

      // 添加按键抬起动作
      const hidKeyCode = getHIDKeyCode(event.keyCode);
      if (hidKeyCode !== 0x00) {
        setRecordedActions(prev => [...prev, {
          type: 'keyup',
          keyCode: hidKeyCode,
          timestamp: now - recordStartTime.current
        }]);
      }

      lastActionTime.current = now;
      event.preventDefault();
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isRecording, recordedActions, delayMode, fixedDelay]);

  const startRecording = () => {
    setRecordedActions([]);
    setIsRecording(true);
    recordStartTime.current = null;
    lastActionTime.current = null;
    messageApi.info(t('macro.recording_started', { index: currentMacro }));
  };

  const stopRecording = () => {
    setIsRecording(false);
    if (recordedActions.length > 0) {
      // 保存录制的动作到对应的宏键
      setMacroList(prev => {
        const newList = [...prev];
        newList[currentMacro] = {
          ...newList[currentMacro],
          actions: recordedActions,
          description: t('macro.contains_actions', { count: recordedActions.filter(a => a.type === 'keydown').length })
        };

        updateMacroToDevice(newList);
        return newList;
      });
      messageApi.success(t('macro.recording_completed', { index: currentMacro }));
    } else {
      messageApi.warning(t('macro.no_actions_recorded'));
    }
    setRecordedActions([]);
  };

  // 补充删除宏的逻辑
  const deleteMacro = (macroIndex) => {
    Modal.confirm({
      title: t('macro.confirm_clear_title', { index: macroIndex }),
      content: t('macro.confirm_clear_content'),
      onOk: () => {
        setMacroList(prev => {
          const newList = [...prev];
          // 清空该宏的动作
          newList[macroIndex] = {
            ...newList[macroIndex],
            actions: [],
            description: `${t('macro.macro_key')} ${macroIndex}`
          };

          updateMacroToDevice(newList);
          return newList;
        });
        messageApi.success(t('macro.clear_success', { index: macroIndex }));
      }
    });
  };

  // 更新宏数据到设备
  const updateMacroToDevice = (newMacroList) => {
    let allEncodedData = [];
    for (let macroIdx = 0; macroIdx <= 15; macroIdx++) {
      const actions = newMacroList[macroIdx] && Array.isArray(newMacroList[macroIdx].actions)
        ? newMacroList[macroIdx].actions
        : [];
      let encodedData = encodeMacroData(actions);
      encodedData.push(0x00);
      allEncodedData = allEncodedData.concat(encodedData);
    }
    setMacroConfig(dataQueue, 0, allEncodedData);
  };

  // 新增：删除某个按键和延时组合
  const handleDeleteAction = (record) => {
    // record: 表格中的一行（合并后的）
    const newMacroList = [...macroList];
    const currentActions = [...newMacroList[currentMacro].actions];

    // 重新构建 getTableData 的映射，找到 record.id 对应的原始 actions 索引
    const tableData = getTableData(currentActions);

    // 找到当前 record 在 tableData 中的索引
    const tableIdx = tableData.findIndex(a => a.id === record.id);
    if (tableIdx === -1) return;

    // 找到原始 actions 中的索引
    let actionIdx = -1;
    let delayIdx = -1;
    let count = -1;
    for (let i = 0; i < currentActions.length; i++) {
      if (currentActions[i].type === 'keydown' || currentActions[i].type === 'keyup') {
        count++;
        if (count === tableIdx) {
          actionIdx = i;
          // 检查下一个是不是 delay
          if (i + 1 < currentActions.length && currentActions[i + 1].type === 'delay') {
            delayIdx = i + 1;
          }
          break;
        }
      }
    }

    // 删除按键动作
    if (actionIdx !== -1) {
      // 删除按键动作
      currentActions.splice(actionIdx, 1);
      // 如果有延时，删除延时
      if (delayIdx !== -1) {
        // 注意：删除按键后，delayIdx-1
        currentActions.splice(delayIdx - 1, 1);
      }
    }

    newMacroList[currentMacro] = {
      ...newMacroList[currentMacro],
      actions: currentActions
    };

    setMacroList(newMacroList);
    updateMacroToDevice(newMacroList);
  };

  // 编辑动作
  // 新实现：支持最后一个延时的编辑
  const handleEditAction = (record, field, value) => {
    // record: 表格中的一行（合并后的），field: 'delay'，value: 新的延时
    const newMacroList = [...macroList];
    const currentActions = [...newMacroList[currentMacro].actions];

    // 重新构建 getTableData 的映射，找到 record.id 对应的原始 actions 索引
    // 由于 getTableData 合并了 delay 到前一个按键动作，所以我们需要找到原始 actions 中的按键动作和其后跟的 delay
    const tableData = getTableData(currentActions);

    // 找到当前 record 在 tableData 中的索引
    const tableIdx = tableData.findIndex(a => a.id === record.id);
    if (tableIdx === -1) return;

    // 找到原始 actions 中的索引
    let actionIdx = -1;
    let delayIdx = -1;
    let count = -1;
    for (let i = 0; i < currentActions.length; i++) {
      if (currentActions[i].type === 'keydown' || currentActions[i].type === 'keyup') {
        count++;
        if (count === tableIdx) {
          actionIdx = i;
          // 检查下一个是不是 delay
          if (i + 1 < currentActions.length && currentActions[i + 1].type === 'delay') {
            delayIdx = i + 1;
          }
          break;
        }
      }
    }

    // 如果下一个是 delay，直接改
    if (delayIdx !== -1) {
      currentActions[delayIdx] = {
        ...currentActions[delayIdx],
        duration: value
      };
    } else {
      // 没有 delay，说明是最后一个按键动作后没有延时，需要插入
      // 但如果是最后一个按键动作，允许插入 delay
      if (actionIdx !== -1) {
        // 插入到 actionIdx+1
        currentActions.splice(actionIdx + 1, 0, {
          type: 'delay',
          duration: value,
          timestamp: (currentActions[actionIdx]?.timestamp || 0) + 1 // 保证唯一
        });
      }
    }

    newMacroList[currentMacro] = {
      ...newMacroList[currentMacro],
      actions: currentActions
    };

    setMacroList(newMacroList);
    updateMacroToDevice(newMacroList);
  };

  // 表格列定义
  const getTableColumns = () => [
    {
      title: t('macro.key_column'),
      align: 'center',
      dataIndex: 'key',
      key: 'key',
      width: 120,
      render: (_, record) => {
        return (
          <Tag color={record.type === 'keydown' ? 'green' : 'green'}>
            {findNameByCode(`00 ${Number(record.keyCode).toString(16).padStart(2, '0').toUpperCase()}`)}
          </Tag>
        );
      }
    },
    {
      title: t('macro.action_column'),
      align: 'center',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type) => {
        switch (type) {
          case 'keydown':
            return <img src={pressIcon} alt="press" style={{width: '2em', height: '2em'}} />;
          case 'keyup':
            return <img src={releaseIcon} alt="release" style={{width: '2em', height: '2em'}} />;
        }
      }
    },
    {
      title: t('macro.delay_column'),
      align: 'center',
      dataIndex: 'delay',
      key: 'delay',
      width: 120,
      render: (delay, record) => {
        if (editingKey === record.id) {
          return (
            <InputNumber
              size="small"
              defaultValue={delay || 0}
              min={0}
              max={100000}
              onChange={(value) => {
                if (value !== delay && value !== null && value !== undefined) {
                  setEditingDuration(value);
                }
              }}
              onPressEnter={() => setEditingKey(null)}
              onBlur={() => setEditingKey(null)}
              autoFocus
            />
          );
        }

        return <Text>{delay || 0} {t('macro.ms')}</Text>;
      }
    },
    {
      title: t('macro.operation_column'),
      align: 'center',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          {editingKey === record.id ? (
            <>
              <Button
                type="link"
                size="small"
                icon={<SaveOutlined />}
                htmlType="submit"
                tabIndex={0}
                onMouseDown={e => e.preventDefault()}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    // 保存编辑内容
                    handleEditAction(record, 'delay', editingDuration);
                    setEditingKey(null);
                  }
                }}
                onClick={e => {
                  handleEditAction(record, 'delay', editingDuration);
                  setEditingKey(null);
                }}
              />
              <Button
                type="link"
                size="small"
                icon={<CloseOutlined />}
                onClick={() => setEditingKey(null)}
              />
            </>
          ) : (
            <>
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => {
                  setEditingKey(record.id);
                  setEditingDuration(record.delay);
                }}
              />
              <Popconfirm
                title={t('macro.confirm_delete_action')}
                onConfirm={() => handleDeleteAction(record)}
                okText={t('macro.delete')}
                cancelText={t('macro.cancel')}
              >
                <Button
                  type="link"
                  size="small"
                  icon={<DeleteOutlined />}
                  danger
                />
              </Popconfirm>
            </>
          )}
        </Space>
      )
    }
  ];

  // 准备表格数据 - 将延时动作合并到前一个按键动作中
  // 支持最后一个按键动作后没有 delay 的情况
  const getTableData = (actions) => {
    if (!actions || actions.length === 0) {
      return [];
    }

    const processedActions = [];

    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];

      if (action.type === 'delay') {
        // 如果是延时动作，将其合并到前一个按键动作中
        if (processedActions.length > 0) {
          processedActions[processedActions.length - 1].delay = action.duration;
        }
      } else if (action.type === 'keydown' || action.type === 'keyup') {
        // 如果是按键动作，直接添加
        const processedAction = {
          ...action,
          delay: 0, // 默认延时为0
          id: `${action.type}_${i}_${action.timestamp || i}`,
          key: `${action.type}_${i}_${action.timestamp || i}`
        };

        // 检查下一个动作是否是延时
        if (i + 1 < actions.length && actions[i + 1].type === 'delay') {
          processedAction.delay = actions[i + 1].duration;
          i++; // 跳过下一个延时动作
        }

        processedActions.push(processedAction);
      }
    }

    // 检查最后一个按键动作后是否有 delay，如果没有，允许用户编辑最后一个延时
    if (processedActions.length > 0) {
      const lastAction = processedActions[processedActions.length - 1];
      // 查找原始 actions 是否最后是按键动作且后面没有 delay
      let lastIdx = actions.length - 1;
      while (lastIdx >= 0 && actions[lastIdx].type === 'delay') lastIdx--;
      if (lastIdx >= 0 && (actions[lastIdx].type === 'keydown' || actions[lastIdx].type === 'keyup')) {
        // 如果 processedActions 最后一个 delay 为 0，说明没有 delay
        if (!lastAction.delay || lastAction.delay === 0) {
          // 允许编辑最后一个延时
          // 不需要额外处理，表格会显示 delay: 0，可编辑
        }
      }
    }

    return processedActions;
  };

  // 渲染动作表格
  const renderActionTable = (actions) => {
    const tableData = getTableData(actions);

    if (tableData.length === 0) {
      return <Text type="secondary">{t('macro.no_actions')}</Text>;
    }

    return (
      <ConfigProvider
        theme={{
          components: {
            Table: {
              headerBg: '#17181C',
              rowHoverBg: '#17181C',
            },
          },
        }}
      >
        <Table
          rowClassName='macro-table-row'
          columns={getTableColumns()}
          dataSource={tableData}
          pagination={false}
          size="small"
          scroll={{ y: 290 }}
          style={{ marginTop: 8, background: 'transparent' }}
        />
      </ConfigProvider>
    );
  };

  return (
    <div className="macro-container">
      {contextHolder}
      <div style={{display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginTop: '2em'}}>
        <div className="d-flex">
          <div>
            <div className="d-flex align-items-center" style={{justifyContent: 'space-between'}}>
              <div className="d-flex">
                <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em'}}></div>
                <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>{t('macro.title')}</div>
              </div>
            </div>
            <div
              style={{
                width: '352px',
                overflow: 'auto',
                borderRadius: '1em',
                marginRight: '1em',
                padding: '2em',
                alignContent: 'flex-start',
              }}
              className='custom-card-container'
            >
              <div style={{width: '100%'}}>
                <div>
                  <div>{t('macro.assign_macro')}</div>
                  <Select
                    style={{ width: '100%', marginTop: '8px' }}
                    value={currentMacro}
                    onChange={value => setCurrentMacro(value)}
                    disabled={isRecording}
                  >
                    {macroList.map((item, idx) => (
                      <Option key={item.id} value={idx}>{item.name}</Option>
                    ))}
                  </Select>
                </div>
                <div style={{ marginTop: '24px' }}>
                  <div style={{ fontWeight: 500, marginBottom: 8 }}>{t('macro.interval_setting')}</div>
                  <Radio.Group
                    value={delayMode}
                    onChange={e => setDelayMode(e.target.value)}
                    disabled={isRecording}
                  >
                    <Space direction="vertical">
                      <Radio value="recorded">{t('macro.use_recorded_interval')}</Radio>
                      <Radio value="fixed">
                        {t('macro.use_standard_interval')}
                        <InputNumber
                          min={0}
                          max={10000}
                          value={fixedDelay}
                          onChange={v => setFixedDelay(v || 0)}
                          style={{ marginLeft: 8, width: 80 }}
                          disabled={delayMode !== 'fixed' || isRecording}
                        />
                        <span style={{ marginLeft: 4 }}>ms</span>
                      </Radio>
                    </Space>
                  </Radio.Group>
                </div>
              </div>
            </div>
          </div>
          <div style={{marginTop: '4px'}}>
            <div className="d-flex justify-content-between" style={{marginTop: '-0.8em'}}>
              <div className="d-flex align-items-center">
                <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em'}}></div>
                <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>{t('macro.recording_title')}</div>
              </div>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <Button
                  key="delete"
                  icon={<DeleteOutlined />}
                  onClick={() => deleteMacro(currentMacro)}
                  disabled={macroList[currentMacro]?.actions.length === 0 || isRecording}
                >
                  {t('macro.clear_data')}
                </Button>
                <Button
                  key="record"
                  color={isRecording ? "danger" : "primary"}
                  icon={isRecording ? <StopOutlined /> : null}
                  variant="solid"
                  onClick={() => {
                    if (isRecording) {
                      stopRecording();
                    } else {
                      startRecording();
                    }
                  }}
                >
                  {isRecording ? t('macro.stop_recording') : t('macro.start_recording')}
                </Button>
              </div>
            </div>
            <div
              style={{
                width: '752px',
                overflow: 'auto',
                borderRadius: '1em',
                padding: '2em',
                alignContent: 'flex-start',
                marginTop: '0.7em'
              }}
              className='custom-card-container'
            >
              {isRecording ? (
                <div>
                  {renderActionTable(recordedActions)}
                </div>
              ) : macroList[currentMacro]?.actions.length > 0 ? (
                <>
                  {/* 显示动作表格 */}
                  {renderActionTable(macroList[currentMacro]?.actions)}
                </>
              ) : (
                <div style={{ textAlign: 'center', color: '#888', fontSize: '1.1em', padding: '2em 0' }}>
                  {t('macro.no_macro_recorded_yet')}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Macro;
