import { useContext, useState } from 'react';
import { KeyboardContext } from './KeyboardContext';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { triggerFocus } from 'antd/es/input/Input';
import { useTranslation } from 'react-i18next';
import processString from './processString';
import forbidden from '../../assets/forbidden.svg'
import '../KeyTest/KeyTest.css'
import { getLayoutJsonByPid } from '../Keyboard/HandleLayout';

// 封装键盘逻辑
const useKeyboardLogic = (layout) => {
  const { data, setCurrentLayer, setCurrentSelectedKey, setCurrentSelectedKeycaps, calibrationKeys } = useContext(KeyboardContext);
  const { setPerformance, backlight, stableMode, adjustTimes, setAdjustTimes, adjustStatus, setAdjustStatus, needAdjustKeys, setNeedAdjustKeys, deviceProductId } = useHandleDevice();
  const activeKeys = data.currentSelectedKeycaps;
  const [isDragging, setIsDragging] = useState(triggerFocus);
  const { t } = useTranslation();

  // 获取按键label
  const getKeyLabel = (row, column) => {
    let original_label = data.keycaps[data.currentLayer][`${row}-${column}`]?.label;
    if (original_label?.includes('<br/>')) {
      if (stableMode === '01') {
        let final_press_label, final_release_label;
        const [press, release] = original_label?.split('<br/>')
        if (stableMode === '01') {
          if (parseFloat(press) <= 0.01) {
            final_press_label = '0.005'
          } else {
            final_press_label = press
          }
          if (parseFloat(release) <= 0.01) {
            final_release_label = '0.005'
          } else {
            final_release_label = release
          }
        } else {
          final_press_label = press
          final_release_label = release
        }
        return `${final_press_label}<br/>${final_release_label}`
      } else {
        return original_label;
      }
    } else {
      return original_label;
    }
  };

  // 获取高级按键类型
  const getKeyAdvancedType = (row, column) => {
    return data.keycaps[data.currentLayer][`${row}-${column}`]?.advancedKeyType;
  };

  // 处理性能层
  const handlePerformanceLayer = (keycaps) => {
    if (keycaps.length === 0) {
      setPerformance(prev => ({
        ...prev,
        activeKey: '',
        triggerPoint: 0,
        pressTriggerPoint: 0,
        releaseTriggerPoint: 0,
        bottomProtectionPoint: 0,
        switchType: '00'
      }));
      return;
    }
    const key = keycaps[0];
    if (key && key.label && key.label.includes('<br/>')) {
      const firstKey = data.keycaps[data.currentLayer][`${key.row}-${key.column}`]
      const triggerPoint = firstKey.rapidTrigger.triggerPoint || 0;
      const pressTriggerPoint = firstKey.rapidTrigger.pressTravel || 0;
      const releaseTriggerPoint = firstKey.rapidTrigger.releaseStroke || 0;
      const bottomProtectionPoint = firstKey.rapidTrigger.bottomProtectionTravel || 0;
      const switchType = firstKey.rapidTrigger.switchType || '00';
      setPerformance(prev => ({
        ...prev,
        activeKey: 'rt',
        triggerPoint,
        pressTriggerPoint,
        releaseTriggerPoint,
        bottomProtectionPoint,
        switchType
      }));
    } else {
      const triggerPoint = key?.label ? parseFloat(key.label) || 0 : 0;
      const firstKey = data.keycaps[data.currentLayer][`${key.row}-${key.column}`]
      const switchType = firstKey.rapidTrigger.switchType || '00';
      setPerformance(prev => ({
        ...prev,
        activeKey: 'basic',
        triggerPoint,
        pressTriggerPoint: 0,
        releaseTriggerPoint: 0,
        bottomProtectionPoint: 0,
        switchType
      }));
    }
  };

  // 鼠标事件
  const handleMouseDown = (row, column, label) => {
    if (data.maxSelection === 0) return;
    setIsDragging(true);
    handleKeycapClick(row, column, label);
  };

  const handleMouseLeave = () => setIsDragging(false);

  const handleMouseEnter = (row, column, label) => {
    if (!isDragging) return;
    if (!Array.isArray(activeKeys) || !activeKeys.some(key => key.row === row && key.column === column)) {
      if (data.maxSelection > 0 && activeKeys.length >= data.maxSelection) {
        if (data.maxSelection === 1) {
          const newActiveKeys = [{ row, column, label }];
          setCurrentSelectedKey({ row, column, label });
          setCurrentSelectedKeycaps(newActiveKeys);
          if (data.currentLayer === 'performance') {
            handlePerformanceLayer(newActiveKeys);
          }
        }
        return;
      }
      const newActiveKeys = [...activeKeys, { row, column, label }];
      setCurrentSelectedKey({ row, column, label });
      setCurrentSelectedKeycaps(newActiveKeys);
      if (data.currentLayer === 'performance') {
        handlePerformanceLayer(newActiveKeys);
      }
    }
  };

  const handleMouseUp = () => setIsDragging(false);

  // 按键点击
  const handleKeycapClick = (row, column, label) => {
    if (data.maxSelection === 0) return;
    const keyPosition = { row, column, label };
    if (Array.isArray(activeKeys) && activeKeys.some(key => key.row === row && key.column === column)) {
      const newActiveKeys = activeKeys.filter(key => !(key.row === row && key.column === column));
      setCurrentSelectedKey({});
      setCurrentSelectedKeycaps(newActiveKeys);
      if (data.currentLayer === 'performance') {
        handlePerformanceLayer(newActiveKeys);
      }
    } else {
      if (data.maxSelection > 0) {
        if (activeKeys.length >= data.maxSelection) {
          if (data.maxSelection === 1) {
            const newActiveKeys = [keyPosition];
            setCurrentSelectedKey(keyPosition);
            setCurrentSelectedKeycaps(newActiveKeys);
            if (data.currentLayer === 'performance') {
              handlePerformanceLayer(newActiveKeys);
            }
          }
          return;
        }
      }
      const newActiveKeys = Array.isArray(activeKeys) ? [...activeKeys, keyPosition] : [keyPosition];
      setCurrentSelectedKey(keyPosition);
      setCurrentSelectedKeycaps(newActiveKeys);
      if (data.currentLayer === 'performance') {
        handlePerformanceLayer(newActiveKeys);
      }
    }
  };

  // 选择WASD
  const handleSelectWASD = () => {
    const layoutJson = getLayoutJsonByPid(deviceProductId);
    const extra_row = layoutJson.length === 6
    let wasdKeys = [];
    if (extra_row) {
      wasdKeys = [
        { row: "02", column: "02", label: getKeyLabel("02", "02") },
        { row: "03", column: "01", label: getKeyLabel("03", "01") },
        { row: "03", column: "03", label: getKeyLabel("03", "03") },
        { row: "03", column: "02", label: getKeyLabel("03", "02") }
      ];
    } else {
      wasdKeys = [
        { row: "01", column: "02", label: getKeyLabel("01", "02") },
        { row: "02", column: "01", label: getKeyLabel("02", "01") },
        { row: "02", column: "03", label: getKeyLabel("02", "03") },
        { row: "02", column: "02", label: getKeyLabel("02", "02") }
      ];
    }
    setCurrentSelectedKeycaps(wasdKeys);
    if (data.currentLayer === 'performance') {
      handlePerformanceLayer(wasdKeys);
    }
  };

  // 全选
  const handleSelectAll = () => {
    const allKeys = [];
    for (let row = 0; row <= 5; row++) {
      for (let col = 0; col <= 16; col++) {
        const rowHex = row.toString().padStart(2, '0');
        const colHex = col.toString(16).toUpperCase().padStart(2, '0');
        if (data.keycaps[data.currentLayer][`${rowHex}-${colHex}`]) {
          allKeys.push({
            row: rowHex,
            column: colHex,
            label: data.keycaps[data.currentLayer][`${rowHex}-${colHex}`].label,
            key: `${rowHex}-${colHex}`
          });
        }
      }
    }
    setCurrentSelectedKeycaps(allKeys);
    handlePerformanceLayer(allKeys);
  };

  // 反选
  const handleInvertSelection = () => {
    const allKeys = [];
    const currentSelected = new Set(activeKeys.map(key => `${key.row}-${key.column}`));
    for (let row = 0; row <= 5; row++) {
      for (let col = 0; col <= 16; col++) {
        const rowHex = row.toString().padStart(2, '0');
        const colHex = col.toString(16).toUpperCase().padStart(2, '0');
        const keyId = `${rowHex}-${colHex}`;
        if (data.keycaps[data.currentLayer][keyId] && !currentSelected.has(keyId)) {
          allKeys.push({
            row: rowHex,
            column: colHex,
            label: data.keycaps[data.currentLayer][keyId].label,
            key: keyId
          });
        }
      }
    }
    setCurrentSelectedKeycaps(allKeys);
    handlePerformanceLayer(allKeys);
  };

  // 清空
  const handleClearSelection = () => {
    setCurrentSelectedKeycaps([]);
    if (data.currentLayer === 'performance') {
      handlePerformanceLayer([]);
    }
  };

  // 渲染单个Keycap
  const renderKeycap = (props) => {
    const {
      row,
      column,
      size = 'u1',
      label,
      style = {},
      advancedKeyType = '',
      key: keyProp // Accept key prop for unique key assignment
    } = props;

    const keyId = `${data.currentLayer}-${row}-${column}`;
    const keyData = data.keycaps?.[keyId] || { label };
    const keyColor = data.keycaps?.[`${data.currentLayer}`][`${row}-${column}`]?.color;
    const isActive = Array.isArray(activeKeys) && activeKeys.some(key => key.row === row && key.column === column);
    let content = "";
    let newClass = false;
    let styleCopy = { ...(style || {}) };

    if (advancedKeyType === 'rs' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>RS<br/><span style='color: var(--bs-primary);'>一</span></div>"
    } else if (advancedKeyType === 'socd' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>SOCD<br/><span style='color: var(--bs-primary);'>一</span></div>"
    } else if (advancedKeyType === 'mt' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>MT<br/><span style='color: var(--bs-primary);'>一</span></div>"
    } else if (advancedKeyType === 'dks' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>DKS<br/><span style='color: var(--bs-primary);'>一</span></div>"
    } else if (advancedKeyType === 'tgl' && data.showAdvancedKey) {
      content = "<div style='margin-top: 6px;text-align: center;'>TGL<br/><span style='color: var(--bs-primary);'>一</span></div>"
    } else {
      content = keyData.label || label
    }

    if (data.menuItem === "keymap") {
      let contentdo = "";
      contentdo = processString(content, 'picture')
      let id = contentdo + '-iconid'
      content = `<div class='keyicon_class' id='${id}'>
           <svg
          aria-hidden="true"
          width=42
          height=42
          >
          <use href= #icon-${contentdo === '' ? 'default' : contentdo} />
        </svg>
        </div>`
    } else if (['socd', 'mt', 'dks', 'rs', 'tgl'].includes(advancedKeyType) && data.showAdvancedKey) {
      // 特殊按键不处理
    } else {
      content = processString(content, 'text')
    }

    if (data.menuItem === "light" && backlight.mode === 46) {
      content = `<div style='margin-top: 0px;text-align: center;width: 100%;'>
        <div style='height: 24px;display:flex;align-items:center;justify-content:center;' class='keycap-text'>${content}</div>
        <div style='background-color: ${keyColor};height: 3px;width: calc(100% - 2em);margin-top: 10px;margin-left: 1em;'></div></div>`
    }

    if (data.menuItem === 'performance') {
      let original_label = data.keycaps['00'][`${row}-${column}`]?.label;
      const contentn = processString(original_label, 'text')
      content = ` <span class="keycap-inside-up">${contentn}</span> <span
            class=" keycap-inside-dou"> ${content}</span>`
      newClass = true
    }

    if (data.menuItem === 'keytest') {
      const currentKeycap = data.keycaps[data.currentLayer][`${row}-${column}`];
      // To avoid mutating a potentially frozen or shared style object, always clone it first
      if ((needAdjustKeys.length === 0) || needAdjustKeys.includes(`${row}-${column}`)) {
        if (adjustStatus === 'start') {
          // 进行中
          const SPECIAL_ICON_BASE = '/keytest/numbers/';
          content = `<div
              class="keytest-keycap-button-start"
              style='background-image: url(${SPECIAL_ICON_BASE + adjustTimes + '0' + (currentKeycap.alreadyAdjustTimes < adjustTimes ? (adjustTimes - currentKeycap.alreadyAdjustTimes) : 0)}.svg)'
            ></div>`;
          newClass = true;
          if (currentKeycap.alreadyAdjustTimes < adjustTimes) {
            styleCopy.border = '2px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0))';
            styleCopy.background = '#48484e';
          } else {
            styleCopy.border = '2px solid #6EB1FF';
            styleCopy.background = 'linear-gradient(0deg, #40a0ff26, #40a0ff26), linear-gradient(0deg, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, .15)) 0%, var(--fill-new-fill-new-fill-43, rgba(235, 237, 250, .15)) 100%), var(--special-special-button-2, #2C2C30)';
          }
        } else {
          // 常规显示
          const realtime_voltage = currentKeycap?.realtimeVoltage || 0;
          const max_voltage = currentKeycap?.maxKeyVoltage || 0;
          const min_voltage = currentKeycap?.minKeyVoltage || 0;

          content = `<div style='display: flex;flex-direction: column;align-items: center;justify-content: center;'>
            <div style='color: #d7d7db;'>${realtime_voltage}</div>
            <div style='color: #818287;'>${max_voltage}</div>
            <div style='color: #818287;'>${min_voltage}</div>
          </div>`;
          newClass = true;

          styleCopy.border = '2px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0))';
          styleCopy.background = '#48484e';
        }
      } else {
        // 被禁用
        content = `<div className="keytest-keycap-button-forbidden">
          <img src="${forbidden}" alt="forbidden" />
        </div>`;
        newClass = true;

        styleCopy.border = '2px solid var(--stroke-stroke-42, rgba(255, 255, 255, 0))';
        styleCopy.background = 'linear-gradient(0deg, var(--fill-new-fill-new-fill-5, rgba(29, 30, 32, .5)) 0%, var(--fill-new-fill-new-fill-5, rgba(29, 30, 32, .5)) 100%), var(--special-special-button-2, #2C2C30)';
      }
    }

    // Always provide a unique key prop for the root element
    const uniqueKey = keyProp || `${data.currentLayer}-${row}-${column}`;

    if (data.menuItem !== 'keytest') {
      return (
        <div
          key={uniqueKey}
          className={`keycap ${data.menuItem === 'performance' ? 'keycap-dou' : ''} keycap-${size} ${isActive ? 'active' : ''} ${(advancedKeyType === 'socd' || advancedKeyType === 'mt' || advancedKeyType === 'dks' || advancedKeyType === 'rs' || advancedKeyType === 'tgl') && data.showAdvancedKey ? 'advanced-key' : ''}`}
          onMouseDown={() => handleMouseDown(row, column, label)}
          onMouseEnter={() => handleMouseEnter(row, column, label)}
          onMouseUp={handleMouseUp}
          style={styleCopy}
        >
          <span
            className={newClass ? 'keycap-inside keycap-dou-in' : 'keycap-inside'}
            dangerouslySetInnerHTML={{
              __html: content
            }}
          />
        </div>
      );
    } else {
      return (
        <div
          key={uniqueKey}
          className={`keycap  ${data.menuItem === 'performance' ? 'keycap-dou' : ''}  keycap-${size} ${isActive ? 'active' : ''} ${(advancedKeyType === 'socd' || advancedKeyType === 'mt' || advancedKeyType === 'dks' || advancedKeyType === 'rs' || advancedKeyType === 'tgl') && data.showAdvancedKey ? 'advanced-key' : ''}`}
          // onMouseDown={() => handleMouseDown(row, column, label)}
          // onMouseEnter={() => handleMouseEnter(row, column, label)}
          // onMouseUp={handleMouseUp}
          style={{...styleCopy, border: styleCopy && styleCopy.border ? styleCopy.border : '2px solid #1C1D22'}}
        >
          <span
            className={newClass ? 'keycap-inside keycap-dou-in' : 'keycap-inside'}
            style={{
              background: styleCopy && styleCopy.background ? styleCopy.background : '#1C1D22'
            }}
            dangerouslySetInnerHTML={{
              __html: content
            }}
          />
        </div>
      );
    }
  };

  return {
    data,
    t,
    isDragging,
    setCurrentLayer,
    handleMouseLeave,
    renderKeycap,
    getKeyLabel,
    getKeyAdvancedType,
    handleSelectWASD,
    handleSelectAll,
    handleInvertSelection,
    handleClearSelection
  };
}

export default useKeyboardLogic;