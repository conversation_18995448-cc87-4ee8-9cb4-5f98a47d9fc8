import React, { useState, useRef, useEffect } from "react";
import { Form, Input, Button, Upload, message, FloatButton, Card, Image } from "antd";
import { UploadOutlined, MinusOutlined } from "@ant-design/icons";
import feedbackIcon from '../../assets/feedback.svg';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';

const { TextArea } = Input;

export default function Feedback() {
  const [visible, setVisible] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const formRef = useRef(null);
  const { deviceProductId, firmwareVersion, sn } = useHandleDevice();

  const openForm = () => setVisible(true);
  // 最小化时不清空内容
  const minimizeForm = () => setVisible(false);

  // 粘贴截图上传，限制最多3张
  useEffect(() => {
    const handlePaste = (e) => {
      if (!visible) return; // 仅在表单打开时处理
      const items = e.clipboardData?.items;
      if (!items) return;

      // 计算还能上传几张
      let remain = 3 - fileList.length;
      if (remain <= 0) {
        message.warning('最多可以上传3张反馈图片');
        return;
      }

      let added = 0;
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf("image") !== -1 && added < remain) {
          const file = item.getAsFile();
          if (file) {
            uploadFile(file);
            added++;
          }
        }
      }
      if (added === 0 && remain <= 0) {
        message.warning('最多可以上传3张反馈图片');
      }
    };

    document.addEventListener("paste", handlePaste);
    return () => document.removeEventListener("paste", handlePaste);
  }, [visible, fileList.length]);

  useEffect(() => {
    console.log(fileList);
  }, [fileList]);

  const uploadFile = (file) => {
    if (fileList.length >= 3) {
      message.warning('最多可以上传3张反馈图片');
      return;
    }
    const formData = new FormData();
    formData.append("image", file);

    fetch(`${import.meta.env.VITE_API_URL}/api/uploads/image`, { method: "POST", body: formData })
      .then((res) => res.json())
      .then((data) => {
        if (data.success) {
          message.success("图片上传成功");
          setFileList((prev) => [
            ...prev,
            { uid: Date.now().toString(), name: file.name, status: "done", url: data.data.url },
          ]);
        } else {
          message.error("图片上传失败");
        }
      })
      .catch(() => message.error("上传失败"));
  };

  // 辅助函数：将文件转为base64
  function getBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  }

  const handlePreview = async file => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  };

  const handleSubmit = async (values) => {
    if (submitting) return;
    setSubmitting(true);
    const payload = {
      ...values,
      images: fileList.map((f) => f.url),
      pid: deviceProductId || undefined,
      sn_code: sn || undefined,
      version: firmwareVersion || undefined,
    };
    try {
      // 提交到后台
      const res = await fetch(`${import.meta.env.VITE_API_URL}/api/feedbacks`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(payload)
      });
      const data = await res.json();
      if (data.status === 201) {
        message.success("反馈已提交！");
        // 只有提交时才清空内容
        setVisible(false);
        setFileList([]);
        // 清空表单内容
        if (formRef.current) {
          formRef.current.resetFields();
        }
      } else {
        message.error(data.message || "提交失败，请稍后重试");
      }
    } catch (err) {
      message.error("提交失败，请检查网络或稍后重试");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      {/* 悬浮按钮 */}
      <FloatButton
        icon={<img src={feedbackIcon} alt="反馈" style={{ width: 19, height: 19 }} />}
        tooltip="反馈问题"
        type="primary"
        onClick={openForm}
        style={{ transform: 'scale(1.15)' }}
      />

      {/* 小浮窗 */}
      <div style={{ display: visible ? "block" : "none" }}>
        <Card
          size="small"
          title="提交反馈"
          extra={<MinusOutlined onClick={minimizeForm} style={{ cursor: "pointer" }} />}
          style={{
            position: "fixed",
            bottom: 24,
            right: 24,
            width: 350,
            zIndex: 1000,
            maxHeight: '80vh',
            overflowY: 'auto',
            height: 'auto',
            minHeight: '520px',
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
          }}
        >
          <Form layout="vertical" onFinish={handleSubmit} ref={formRef}>
            {/* <Form.Item
              label="标题"
              name="title"
              rules={[{ required: true, message: "请输入标题" }]}
            >
              <Input placeholder="请输入标题"  />
            </Form.Item> */}

            <Form.Item
              label="联系方式"
              name="contact"
              rules={[{ required: false, message: "请输入联系方式" }]}
            >
              <Input placeholder="请输入联系方式"  />
            </Form.Item>

            <Form.Item
              label="问题描述（支持粘贴图片）"
              name="description"
              rules={[{ required: true, message: "请输入问题描述" }]}
            >
              <TextArea
                placeholder="请描述您的问题"
                rows={6}
                maxLength={500}
                showCount={true}
              />
            </Form.Item>

            <div style={{ height: '120px' }}>
              <Upload
                fileList={fileList}
                listType="picture-card"
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true,
                }}
                beforeUpload={(file, fileListNew) => {
                  if (fileList.length >= 3) {
                    message.warning('最多可以上传3张反馈图片');
                    return Upload.LIST_IGNORE;
                  }
                  return false;
                }}
                onRemove={(file) => setFileList(fileList.filter((f) => f.uid !== file.uid))}
                onPreview={handlePreview}
              >
              </Upload>
            </div>

            {previewImage && (
              <Image
                wrapperStyle={{ display: 'none' }}
                preview={{
                  visible: previewOpen,
                  onVisibleChange: visible => setPreviewOpen(visible),
                  afterOpenChange: visible => !visible && setPreviewImage(''),
                }}
                src={previewImage}
              />
            )}
            <Form.Item>
              <Button type="primary" htmlType="submit" block loading={submitting} disabled={submitting}>
                提交
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </>
  );
}
