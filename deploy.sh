#!/usr/bin/env bash

# Vite 一键打包并发布到阿里云 OSS（macOS）
# -------------------------------------------------
# 使用方法：
# 1) 复制本脚本为 deploy.sh，并赋予可执行权限：chmod +x deploy.sh
# 2) 在项目根目录创建 .deploy.env（可参考下方示例）
# 3) 执行 ./deploy.sh
#
# 说明：
# - 默认使用当前用户已配置好的 ~/.ossutilconfig。如果你希望在 CI 或本机无配置地使用，
#   请在 .deploy.env 中提供 OSS_ACCESS_KEY_ID / OSS_ACCESS_KEY_SECRET（与可选的临时 STS 令牌）。
# - 如果你未安装 ossutil，请先安装：https://help.aliyun.com/zh/oss/developer-reference/ossutil-overview
#   （macOS 通常为可执行文件 ossutil 或 ossutil64）。
# - 脚本会优先选择 pnpm>yarn>bun>npm 来执行构建。

set -euo pipefail

ROOT_DIR=$(cd "$(dirname "$0")" && pwd)
cd "$ROOT_DIR"

# 读取环境变量
ENV_FILE=".deploy.env"
if [[ -f "$ENV_FILE" ]]; then
  # shellcheck disable=SC2046
  export $(grep -E "^[A-Za-z_][A-Za-z0-9_]*=" "$ENV_FILE" | xargs -I {} echo {})
fi

# 必填项校验
: "${OSS_BUCKET:?请在 .deploy.env 中设置 OSS_BUCKET，如 example-bucket}"
: "${OSS_ENDPOINT:?请在 .deploy.env 中设置 OSS_ENDPOINT，如 oss-cn-shenzhen.aliyuncs.com}"

# region 相关处理（为 sign version 4 提供 region）
# OSS_REGION 可在 .deploy.env 中设置，如 cn-shenzhen
if [[ -z "${OSS_REGION:-}" ]]; then
  # 尝试从 endpoint 推断 region
  if [[ "$OSS_ENDPOINT" =~ oss-([a-z0-9-]+)\.aliyuncs\.com ]]; then
    OSS_REGION="${BASH_REMATCH[1]}"
  else
    echo "❌ 未检测到 OSS_REGION，且无法从 OSS_ENDPOINT 推断。请在 .deploy.env 中设置 OSS_REGION（如 cn-shenzhen）。" >&2
    exit 1
  fi
fi

# 可选项与默认值
DIST_DIR=${DIST_DIR:-dist}
OSS_PREFIX=${OSS_PREFIX:-}
# 若你希望强制使用某个二进制名，可在 .deploy.env 中设置 OSSUTIL_PATH
OSSUTIL_PATH=${OSSUTIL_PATH:-ossutil}
if ! command -v "$OSSUTIL_PATH" >/dev/null 2>&1; then
  # 常见别名回退
  if command -v ossutil64 >/dev/null 2>&1; then
    OSSUTIL_PATH=ossutil64
  else
    echo "❌ 未找到 ossutil，请先安装并确保在 PATH 中（可执行名通常为 ossutil 或 ossutil64）。" >&2
    exit 1
  fi
fi

# 检查 ossutil 配置文件是否存在且包含有效的 AK/SK
check_ossutil_credentials() {
  local config_file="${OSSUTIL_CONFIG_FILE:-$HOME/.ossutilconfig}"
  if [[ ! -f "$config_file" ]]; then
    echo "❌ 未找到 ossutil 配置文件（$config_file）。请确保已配置 AK/SK。" >&2
    exit 1
  fi
  # 检查 accessKeyID 和 accessKeySecret 是否存在且非空
  local ak sk
  ak=$(grep -E "^accessKeyID=" "$config_file" | head -n1 | cut -d= -f2-)
  sk=$(grep -E "^accessKeySecret=" "$config_file" | head -n1 | cut -d= -f2-)
  if [[ -z "$ak" || -z "$sk" ]]; then
    echo "❌ ossutil 配置文件（$config_file）缺少 accessKeyID 或 accessKeySecret。" >&2
    echo "请在 .deploy.env 中设置 OSS_ACCESS_KEY_ID 和 OSS_ACCESS_KEY_SECRET，或正确配置 ~/.ossutilconfig。" >&2
    exit 1
  fi
}

# 如果提供了 AK/SK（和可选 STS），则使用临时配置文件，避免污染全局配置
# TMP_CONFIG=""
# if [[ -n "${OSS_ACCESS_KEY_ID:-}" && -n "${OSS_ACCESS_KEY_SECRET:-}" ]]; then
#   TMP_CONFIG=$(mktemp)
#   {
#     echo ":config"
#     echo "endpoint=$OSS_ENDPOINT"
#     echo "accessKeyID=$OSS_ACCESS_KEY_ID"
#     echo "accessKeySecret=$OSS_ACCESS_KEY_SECRET"
#     echo "region=$OSS_REGION"
#     if [[ -n "${OSS_SECURITY_TOKEN:-}" ]]; then
#       echo "stsToken=$OSS_SECURITY_TOKEN"
#     fi
#   } >"$TMP_CONFIG"
#   export OSSUTIL_CONFIG_FILE="$TMP_CONFIG"
#   echo "⚙️  使用临时 ossutil 配置文件：$TMP_CONFIG"
# else
#   echo "⚙️  使用默认的 ~/.ossutilconfig（或环境变量中的配置）"
# fi

# # 检查 ossutil 配置文件和 AK/SK
# check_ossutil_credentials

# 选择构建环境
select_env() {
  echo "请选择要构建的环境："
  echo "1) 正式站（production）"
  echo "2) Beta站（beta）"
  echo "3) Alpha站（alpha）"
  read -rp "输入序号 [1/2/3]，直接回车为正式站: " env_choice

  case "$env_choice" in
    2)
      VITE_ENV="beta"
      ;;
    3)
      VITE_ENV="alpha"
      ;;
    *)
      VITE_ENV="production"
      ;;
  esac
}

# 选择包管理器并构建
build() {
  select_env
  echo "🚧 构建 Vite 应用…"
  local build_cmd=""
  if [[ -f pnpm-lock.yaml ]] && command -v pnpm >/dev/null 2>&1; then
    if [[ "$VITE_ENV" == "production" ]]; then
      build_cmd="pnpm -s build"
    else
      build_cmd="pnpm -s build --mode $VITE_ENV"
    fi
  elif [[ -f yarn.lock ]] && command -v yarn >/dev/null 2>&1; then
    if [[ "$VITE_ENV" == "production" ]]; then
      build_cmd="yarn build"
    else
      build_cmd="yarn build --mode $VITE_ENV"
    fi
  elif [[ -f bun.lockb ]] && command -v bun >/dev/null 2>&1; then
    if [[ "$VITE_ENV" == "production" ]]; then
      build_cmd="bun run build"
    else
      build_cmd="bun run build --mode $VITE_ENV"
    fi
  else
    if [[ "$VITE_ENV" == "production" ]]; then
      build_cmd="npm run build"
    else
      build_cmd="npm run build -- --mode $VITE_ENV"
    fi
  fi

  echo "执行构建命令: $build_cmd"
  eval "$build_cmd"
}

# 同步到 OSS
upload() {
  local bucket_uri="oss://$OSS_BUCKET"
  if [[ -n "$OSS_PREFIX" ]]; then
    # 去掉可能多余的斜杠
    OSS_PREFIX=${OSS_PREFIX#/}
    OSS_PREFIX=${OSS_PREFIX%/}
    bucket_uri+="/$OSS_PREFIX"
  fi

  if [[ ! -d "$DIST_DIR" ]]; then
    echo "❌ 构建输出目录 $DIST_DIR 不存在。" >&2
    exit 1
  fi

  # 在上传前输出关键信息并二次确认
  current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
  echo "🌏 构建环境: ${VITE_ENV:-production}"
  echo "🌿 当前分支: $current_branch"
  echo "✅ 即将发布到: $bucket_uri （本地目录：$DIST_DIR)"
  echo "🕒 当前时间: $(date '+%Y-%m-%d %H:%M:%S')"
  echo
  read -rp "⚠️  确认要将上述内容上传到 OSS 吗？[y/N]: " confirm
  if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "已取消上传。"
    exit 0
  fi

  echo "☁️  同步 $DIST_DIR → $bucket_uri"

  # 1) 先整体覆盖上传（index.html 等设置 no-cache），并删除 OSS 端多余文件
  "$OSSUTIL_PATH" cp -r -f "$DIST_DIR/" "$bucket_uri/"
    # --meta "Cache-Control:no-cache"

  # 2) 对带 hash 的静态资源（通常在 /assets 下）追加长缓存
  # if [[ -d "$DIST_DIR/assets" ]]; then
  #   echo "📦 设置 /assets 为长缓存（immutable）"
  #   "$OSSUTIL_PATH" cp -r -f "$DIST_DIR/assets" "$bucket_uri/assets" \
  #     --meta "Cache-Control:public,max-age=31536000,immutable"
  # fi

  # 3) 可选：为 source map 设置更短缓存（如果你保留了 *.map）
  # if compgen -G "$DIST_DIR/**/*.map" > /dev/null; then
  #   echo "🗺️  调整 *.map 的缓存头为短缓存"
  #   # 遍历所有 map 文件并单独上传元信息
  #   while IFS= read -r -d '' mapf; do
  #     rel=${mapf#"$DIST_DIR/"}
  #     "$OSSUTIL_PATH" cp -f "$mapf" "$bucket_uri/$rel" \
  #       --meta "Cache-Control:public,max-age=600"
  #   done < <(find "$DIST_DIR" -type f -name "*.map" -print0)
  # fi
}

cleanup() {
  if [[ -n "${TMP_CONFIG:-}" && -f "$TMP_CONFIG" ]]; then
    rm -f "$TMP_CONFIG"
  fi
}

trap cleanup EXIT

build
upload

echo "🌏 构建环境: ${VITE_ENV:-production}"
echo "✅ 发布完成: oss://$OSS_BUCKET/${OSS_PREFIX:-} （本地目录：$DIST_DIR)"
echo "🕒 完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
